import { Message } from '../components/shared/types';

export const getSuggestions = async (chatHisory: Message[]) => {
  const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/suggestions`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      chatHistory: chatHisory,
    }),
  });

  const data = (await res.json()) as { suggestions: string[] };

  return data.suggestions;
};
