import type { Metadata, Viewport } from 'next';
import { Montser<PERSON> } from 'next/font/google';
import './globals.css';
import { cn } from '@/lib/utils';
import { Toaster } from 'sonner';
import ThemeProvider from '@/components/theme/Provider';
import Providers from '@/components/shared/Providers';

const montserrat = Montserrat({
  weight: ['300', '400', '500', '700'],
  subsets: ['latin'],
  display: 'swap',
  fallback: ['Arial', 'sans-serif'],
});

export const viewport: Viewport = {
  themeColor: '#1f2937',
};

export const metadata: Metadata = {
  title: process.env.NEXT_PUBLIC_SITE_TITLE,
  description: `${process.env.NEXT_PUBLIC_SITE_TITLE}是一个连接互联网的 AI 智能助手。`,
  manifest: '/manifest.json',
  appleWebApp: {
    capable: true,
    statusBarStyle: 'default',
    title: process.env.NEXT_PUBLIC_SITE_TITLE || 'aiso'
  },
  icons: {
    apple: [
      { url: '/icons/apple-touch-icon.png' }
    ]
  }
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html className="h-full min-h-screen" lang="zh" suppressHydrationWarning>
      <head>
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="theme-color" content="#1f2937" />
      </head>
      <body className={cn('h-full min-h-screen', montserrat.className)}>
        <Providers>
          <ThemeProvider>
            {children}
            <Toaster
              toastOptions={{
                unstyled: true,
                classNames: {
                  toast:
                    'bg-light-primary dark:bg-dark-secondary dark:text-white/70 text-black-70 rounded-lg p-4 flex flex-row items-center space-x-2',
                },
              }}
            />
          </ThemeProvider>
        </Providers>
      </body>
    </html>
  );
}
