'use client';

import { Fragment, useEffect, useRef, useState } from 'react';
import MessageInput from './MessageInput';
import { File } from './ChatWindow';
import { Message } from '../shared/types';
import MessageBox from './MessageBox';
import { cn, isAndroid } from '@/lib/utils';

const Chat = ({
  loading,
  messages,
  sendMessage,
  messageAppeared,
  rewrite,
  fileIds,
  setFileIds,
  files,
  setFiles,
  title,
  autoScroll = true,
  setMessages,
}: {
  messages: Message[];
  sendMessage: (message: string) => void;
  loading: boolean;
  messageAppeared: boolean;
  rewrite: (messageId: string) => void;
  fileIds: string[];
  setFileIds: (fileIds: string[]) => void;
  files: File[];
  setFiles: (files: File[]) => void;
  title?: string;
  autoScroll?: boolean;
  setMessages: (messages: Message[] | ((prev: Message[]) => Message[])) => void;
}) => {
  const [dividerWidth, setDividerWidth] = useState(0);
  const dividerRef = useRef<HTMLDivElement | null>(null);
  const messageEnd = useRef<HTMLDivElement | null>(null);
  const [isAndroidDevice, setIsAndroidDevice] = useState(false);

  useEffect(() => {
    const updateDividerWidth = () => {
      if (dividerRef.current) {
        setDividerWidth(dividerRef.current.scrollWidth);
      }
    };

    updateDividerWidth();

    window.addEventListener('resize', updateDividerWidth);

    return () => {
      window.removeEventListener('resize', updateDividerWidth);
    };
  });

  useEffect(() => {
    if (autoScroll) {
      const container = messageEnd.current?.parentElement;
      if (container && container.scrollHeight > container.clientHeight) {
        messageEnd.current?.scrollIntoView({ behavior: 'smooth' });
      }
    }

    if (messages.length === 1) {
      document.title = `${messages[0].content.substring(0, 30)} - ${process.env.NEXT_PUBLIC_SITE_TITLE}`;
    }
  }, [messages, autoScroll]);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      setIsAndroidDevice(isAndroid());
    }
  }, []);

  return (
    <div
      className={cn(
        "flex flex-col min-h-screen w-full",
      )}
    >
      <div className="flex-1 overflow-auto overflow-x-hidden px-2 sm:px-4 md:px-8 py-2 space-y-6" style={{ overflowX: 'hidden' }}>
        <div className="max-w-full">
          {messages.map((msg, i) => {
            const isLast = i === messages.length - 1;
            return (
              <Fragment key={msg.messageId}>
                <MessageBox
                  key={i}
                  message={msg}
                  messageIndex={i}
                  history={messages}
                  loading={loading}
                  dividerRef={isLast ? dividerRef : undefined}
                  isLast={isLast}
                  rewrite={rewrite}
                  sendMessage={sendMessage}
                  title={title}
                  setMessages={setMessages}
                />
                {!isLast && msg.role === 'assistant' && (
                  <div className="h-px w-full bg-light-secondary dark:bg-dark-secondary" />
                )}
              </Fragment>
            );
          })}
          {loading && !messageAppeared && <MessageBox loading />}
          <div ref={messageEnd} className="h-0" />
        </div>
      </div>
      <div
        className="sticky bottom-0 left-0 right-0 w-full z-10 px-2 sm:px-4 md:px-8 bg-light-primary dark:bg-dark-primary border-t border-light-100 dark:border-dark-200 backdrop-blur-sm"
        style={{ overflow: 'visible' }}
      >
        <div className="py-3">
          <MessageInput
            loading={loading}
            sendMessage={sendMessage}
            fileIds={fileIds}
            setFileIds={setFileIds}
            files={files}
            setFiles={setFiles}
          />
        </div>
      </div>
    </div>
  );
};

export default Chat;
