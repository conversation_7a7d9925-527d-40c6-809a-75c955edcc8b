'use client';

/* eslint-disable @next/next/no-img-element */
import React, { MutableRefObject, useEffect, useState } from 'react';
import { recentMessages } from './ChatWindow';
import { Message } from '../shared/types';
import { cn } from '@/lib/utils';
import {
  BookCopy,
  Disc3,
  Volume2,
  StopCircle,
  Layers3,
  Plus,
} from 'lucide-react';
import Markdown from 'markdown-to-jsx';
import Copy from '../actions/MessageActions/Copy';
import Rewrite from '../actions/MessageActions/Rewrite';
import MessageSources from '../shared/MessageSources';
import { useSpeech } from 'react-text-to-speech';
import { ChevronDown, ChevronUp } from 'lucide-react';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/cjs/styles/prism';
import { getSuggestions } from '@/lib/actions';

const ThinkingContent = ({ content, isExpanded, onToggle, isLoading, isLast }: {
  content: string;
  isExpanded: boolean;
  onToggle: () => void;
  isLoading: boolean;
  isLast: boolean;
}) => {
  return (
    <div
      onClick={onToggle}
      className={cn(
        "text-black/60 dark:text-white/60 bg-light-secondary/30 dark:bg-dark-secondary/30 rounded-lg px-3 py-2 mb-3 text-sm font-normal border border-light-secondary/50 dark:border-dark-secondary/50 relative overflow-hidden",
        "transition-all duration-300 ease-in-out cursor-pointer",
        isLast && isLoading 
          ? "line-clamp-none max-h-none" 
          : isExpanded 
            ? "line-clamp-none max-h-none" 
            : "line-clamp-3 min-h-[3.5rem] max-h-[3.5rem]"
      )}
    >
      <div className="relative h-full leading-5 whitespace-pre-wrap">
        {content}
        {!isExpanded && !isLoading && (
          <div className="absolute inset-0 h-14 bg-gradient-to-b from-transparent via-transparent to-light-secondary/30 dark:to-dark-secondary/30 pointer-events-none" />
        )}
      </div>
      {!isExpanded && !isLoading && (
        <button 
          className={cn(
            "absolute right-2 top-1/2 -translate-y-1/2 transition-all duration-300",
            "hover:bg-black/5 dark:hover:bg-white/5 p-1 rounded-full"
          )}
        >
          <ChevronDown size={12} />
        </button>
      )}
      {isExpanded && !isLoading && (
        <button 
          className={cn(
            "absolute right-2 bottom-2 transition-all duration-300",
            "hover:bg-black/5 dark:hover:bg-white/5 p-1 rounded-full"
          )}
        >
          <ChevronUp size={12} />
        </button>
      )}
    </div>
  );
};

const SuggestionsButton = ({ onClick, children, loading }: { 
  onClick: () => Promise<void>, 
  children: React.ReactNode,
  loading?: boolean 
}) => (
  <button
    onClick={onClick}
    disabled={loading}
    className={cn(
      "text-xs text-black/50 dark:text-white/50 hover:text-black/70 dark:hover:text-white/70 flex items-center space-x-1 px-2 py-1 rounded-md hover:bg-light-secondary/60 dark:hover:bg-dark-secondary/60 transition-all duration-200",
      loading && "opacity-50 cursor-not-allowed"
    )}
  >
    <svg className={cn("w-3 h-3", loading && "animate-spin")} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M21.8883 13.5C21.1645 18.3113 17.013 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C16.1006 2 19.6248 4.46819 21.1679 8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M17 8H21.4C21.7314 8 22 7.73137 22 7.4V3" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
    <span>{children}</span>
  </button>
);

const MessageBox = ({
  message,
  messageIndex,
  history,
  loading,
  dividerRef,
  isLast,
  rewrite,
  sendMessage,
  title,
  setMessages,
}: {
  message?: Message;
  messageIndex?: number;
  history?: Message[];
  loading?: boolean;
  dividerRef?: MutableRefObject<HTMLDivElement | null>;
  isLast?: boolean;
  rewrite?: (messageId: string) => void;
  sendMessage?: (message: string) => void;
  title?: string;
  setMessages?: (messages: Message[] | ((prev: Message[]) => Message[])) => void;
}) => {
  if (!message) return null;

  const [parsedMessage, setParsedMessage] = useState(message.content);
  const [speechMessage, setSpeechMessage] = useState(message.content);
  const [isThinkingExpanded, setIsThinkingExpanded] = useState(false);

  const [thinkingContent, setThinkingContent] = useState('');
  const [mainContent, setMainContent] = useState('');

  const [suggestionsLoading, setSuggestionsLoading] = useState(false);

  useEffect(() => {
    const regex = /\[(\d+)\]/g;
    const thinkRegex = /<think>([\s\S]*?)<\/think>/;
    const thinkMatch = message.content.match(thinkRegex);

    let finalContent = message.content;

    if (message.reasoning_content || thinkMatch) {
      const thinking = message.reasoning_content || (thinkMatch ? thinkMatch[1] : '');
      finalContent = thinkMatch ? message.content.replace(thinkRegex, '').trim() : finalContent;

      setThinkingContent(thinking);
      setMainContent(finalContent);
    } else {
      setThinkingContent('');
      setMainContent(finalContent);
    }

    if (message.role === 'assistant' && message?.sources && message.sources.length > 0) {
      setParsedMessage(
        finalContent.replace(
          regex,
          (_, number) =>
            `<a href="${message.sources?.[number - 1]?.metadata?.url}" target="_blank" className="inline-flex items-center bg-light-secondary/70 dark:bg-dark-secondary/70 px-1 py-0 rounded-full ml-1 no-underline text-xs text-black/70 dark:text-white/70 hover:bg-light-secondary dark:hover:bg-dark-secondary transition-colors">${number}</a>`,
        ),
      );
    } else {
      setParsedMessage(finalContent);
    }

    setSpeechMessage(finalContent.replace(regex, ''));
  }, [message.content, message.sources, message.role, message.reasoning_content, isLast, loading]);

  const { speechStatus, start, stop } = useSpeech({ text: speechMessage });

  return (
    <div>
      {message.role === 'user' && (
        <div className={cn(
          'w-full flex justify-end',
          'pt-1 lg:pt-2'
        )}>
          <div className="max-w-[90vw] sm:max-w-[70vw] md:max-w-[60vw] bg-[#e6f0ff] dark:bg-[#1a2233] rounded-2xl rounded-br-md px-4 py-2 shadow-sm text-black dark:text-white text-base sm:text-lg font-medium break-words">
            {message.content.includes('Summary:') && title ? title : message.content}
          </div>
        </div>
      )}

      {message.role === 'assistant' && (
        <div className="flex flex-col items-start space-y-2 lg:space-y-0 lg:flex-row lg:justify-between pb-3 lg:pb-6">
          <div ref={dividerRef} className="flex flex-col space-y-2 w-full">
            {message.sources && message.sources.length > 0 && (
              <div className="flex flex-col space-y-1.5 mt-2">
                <div className="flex flex-row items-center space-x-2">
                  <BookCopy className="text-black/80 dark:text-white/80" size={16} />
                  <h3 className="text-black/80 dark:text-white/80 font-medium text-sm">
                    来源
                  </h3>
                </div>
                <MessageSources sources={message.sources} />
              </div>
            )}
            <div className="flex flex-col space-y-2">
              <div className="flex flex-row items-center space-x-2 mt-1">
                <Disc3
                  className={cn(
                    'text-black/90 dark:text-white/90',
                    isLast && loading ? 'animate-spin' : 'animate-none',
                  )}
                  size={18}
                />
                <h3 className="text-black/90 dark:text-white/90 font-medium text-base">
                  回答
                </h3>
              </div>
              {thinkingContent && (
                <ThinkingContent
                  content={thinkingContent}
                  isExpanded={isThinkingExpanded}
                  onToggle={() => setIsThinkingExpanded(!isThinkingExpanded)}
                  isLoading={!!loading}
                  isLast={!!isLast}
                />
              )}
              {mainContent && (
                <div className="w-full bg-white dark:bg-[#181f2a] rounded-2xl rounded-tl-md px-4 py-3 shadow-md border border-light-secondary/40 dark:border-dark-secondary/40">
                  <Markdown
                    options={{
                      forceWrapper: true,
                      overrides: {
                        p: {
                          props: {
                            className: 'whitespace-pre-line text-black/80 dark:text-white/80 leading-6 font-normal'
                          }
                        },
                        a: {
                          props: {
                            className: 'text-blue-500 dark:text-blue-400 no-underline hover:underline inline-block align-middle font-normal',
                            target: '_blank',
                            rel: 'noopener noreferrer'
                          }
                        },
                        ul: {
                          props: {
                            className: 'pl-5 list-disc my-1 font-normal'
                          }
                        },
                        ol: {
                          props: {
                            className: 'pl-5 list-decimal my-1 font-normal'
                          }
                        },
                        li: {
                          props: {
                            className: 'text-black/80 dark:text-white/80 mb-0.5 last:mb-0 leading-5 font-normal'
                          }
                        },
                        h1: {
                          props: {
                            className: 'text-lg font-semibold mb-2.5 mt-2.5 text-black/90 dark:text-white/90'
                          }
                        },
                        h2: {
                          props: {
                            className: 'text-base font-medium mb-2 mt-2 text-black/90 dark:text-white/90'
                          }
                        },
                        h3: {
                          props: {
                            className: 'text-sm font-medium mb-1.5 mt-1.5 text-black/90 dark:text-white/90'
                          }
                        },
                        blockquote: {
                          props: {
                            className: 'border-l-4 border-blue-200 dark:border-blue-700 pl-4 italic text-black/70 dark:text-white/70 font-normal bg-blue-50/40 dark:bg-blue-900/10 rounded-md my-2 py-1'
                          }
                        },
                        code: {
                          component: ({ className, children }) => {
                            const match = /language-(\w+)/.exec(className || '');
                            return match ? (
                              <div className="relative group my-2">
                                <div className="absolute right-2 top-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                  <button
                                    onClick={() => {
                                      navigator.clipboard.writeText(String(children));
                                    }}
                                    className="p-1 rounded bg-gray-700/50 hover:bg-gray-700/70 text-white/70 hover:text-white/90 transition-colors"
                                  >
                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M8 5H6C4.89543 5 4 5.89543 4 7V19C4 20.1046 4.89543 21 6 21H16C17.1046 21 18 20.1046 18 19V17M8 5C8 6.10457 8.89543 7 10 7H12C13.1046 7 14 6.10457 14 5M8 5C8 3.89543 8.89543 3 10 3H12C13.1046 3 14 3.89543 14 5M14 5H16C17.1046 5 18 5.89543 18 7V10M20 14V16.5C20 17.3284 19.3284 18 18.5 18H9.5C8.67157 18 8 17.3284 8 16.5V9.5C8 8.67157 8.67157 8 9.5 8H12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                    </svg>
                                  </button>
                                </div>
                                <SyntaxHighlighter
                                  language={match[1]}
                                  style={oneDark}
                                  className="rounded-lg !my-2 !bg-gray-900/90 text-sm"
                                  customStyle={{ padding: '1rem', fontSize: '0.92rem', lineHeight: '1.6', fontFamily: 'monospace' }}
                                  showLineNumbers={String(children).split('\n').length > 5}
                                >
                                  {String(children).replace(/\n$/, '')}
                                </SyntaxHighlighter>
                              </div>
                            ) : (
                              <code className="bg-gray-100 dark:bg-gray-800 px-1.5 py-0.5 rounded-md text-sm text-black/90 dark:text-white/90 font-mono">
                                {children}
                              </code>
                            );
                          }
                        }
                      }
                    }}
                    className={cn(
                      // 基础样式
                      'prose dark:prose-invert max-w-none break-words text-black/80 dark:text-white/80 font-normal',
                      // 标题样式
                      'prose-h1:text-lg prose-h1:font-semibold prose-h1:mb-2.5 prose-h1:mt-2.5 prose-h1:text-black/90 dark:prose-h1:text-white/90',
                      'prose-h2:text-base prose-h2:font-medium prose-h2:mb-2 prose-h2:mt-2 prose-h2:text-black/90 dark:prose-h2:text-white/90',
                      'prose-h3:text-sm prose-h3:font-medium prose-h3:mb-1.5 prose-h3:mt-1.5 prose-h3:text-black/90 dark:prose-h3:text-white/90',
                      // 段落和列表样式
                      'prose-p:leading-6 prose-p:my-1.5 prose-p:text-base prose-p:font-normal',
                      'prose-ul:my-1 prose-ol:my-1 prose-li:my-0 prose-li:text-base prose-li:leading-6 prose-li:font-normal',
                      '[&_ul]:has([type=checkbox]):my-1 [&_li]:has([type=checkbox]):my-0',
                      // 代码块样式
                      'prose-pre:bg-gray-50 dark:prose-pre:bg-gray-900/90 prose-pre:my-2 prose-pre:p-0 prose-pre:rounded-lg prose-pre:shadow-sm',
                      'prose-code:bg-gray-100 dark:bg-gray-800 prose-code:px-1.5 prose-code:py-0.5 prose-code:rounded prose-code:text-sm',
                      'prose-pre:text-black/80 dark:prose-pre:text-white/80 prose-code:text-black/90 dark:prose-code:text-white/90',
                      '[&_code]:before:content-none [&_code]:after:content-none',
                      // 表格样式
                      'prose-table:my-2 prose-table:w-full prose-table:border-collapse prose-table:text-sm',
                      'prose-thead:bg-light-secondary/50 dark:prose-thead:bg-dark-secondary/50',
                      'prose-th:p-1.5 prose-th:border prose-th:border-light-secondary dark:prose-th:border-dark-secondary prose-th:text-left prose-th:font-medium',
                      'prose-td:p-1.5 prose-td:border prose-td:border-light-secondary dark:prose-td:border-dark-secondary prose-td:align-middle',
                      '[&_table]:rounded-md [&_table]:overflow-hidden',
                      // 其他元素样式
                      'prose-hr:my-3 prose-hr:border-gray-200 dark:prose-hr:border-gray-800',
                      'prose-strong:font-medium prose-strong:text-black/90 dark:prose-strong:text-white/90',
                      'prose-a:text-blue-500 dark:text-blue-400 prose-a:no-underline hover:prose-a:underline prose-a:font-normal',
                      'prose-img:rounded-md prose-img:my-2 prose-img:max-w-full prose-img:h-auto',
                      'prose-blockquote:border-l-4 prose-blockquote:border-blue-200 dark:prose-blockquote:border-blue-700 prose-blockquote:pl-4 prose-blockquote:italic prose-blockquote:text-black/70 dark:prose-blockquote:text-white/70 prose-blockquote:font-normal prose-blockquote:bg-blue-50/40 dark:prose-blockquote:bg-blue-900/10 prose-blockquote:rounded-md prose-blockquote:my-2 prose-blockquote:py-1',
                      // 自定义样式 - 减少列表项内部段落的行高
                      '[&_li_p]:leading-5 [&_li_p]:my-0.5 [&_li_p]:font-normal'
                    )}
                  >
                    {parsedMessage}
                  </Markdown>
                </div>
              )}
              {loading && isLast ? null : (
                <div className="flex flex-row items-center justify-between w-full text-black dark:text-white py-0 -mx-2">
                  <div className="flex flex-row items-center space-x-1">
                    <Rewrite rewrite={rewrite ?? (() => {})} messageId={message.messageId} />
                  </div>
                  <div className="flex flex-row items-center space-x-1">
                    <Copy initialMessage={message.content} message={message} />
                    <button
                      hidden
                      onClick={() => {
                        if (speechStatus === 'started') {
                          stop();
                        } else {
                          start();
                        }
                      }}
                      className="p-1.5 text-black/70 dark:text-white/70 rounded-xl hover:bg-light-secondary/60 dark:hover:bg-dark-secondary/60 transition duration-200 hover:text-black dark:hover:text-white"
                    >
                      {speechStatus === 'started' ? (
                        <StopCircle size={16} />
                      ) : (
                        <Volume2 size={16} />
                      )}
                    </button>
                  </div>
                </div>
              )}
              {isLast && message.role === 'assistant' && !loading && (
                <>
                  <div className="h-px w-full bg-light-secondary/70 dark:bg-dark-secondary/70" />
                  <div className="flex flex-col space-y-2 text-black dark:text-white">
                    <div className="flex flex-row items-center justify-between mt-3">
                      <div className="flex flex-row items-center space-x-2">
                        <Layers3 size={16} />
                        <h3 className="text-sm font-medium">相关问题</h3>
                      </div>
                      <SuggestionsButton
                        loading={suggestionsLoading}
                        onClick={async () => {
                          setSuggestionsLoading(true);
                          try {
                            const suggestions = await getSuggestions(recentMessages(history ?? []));
                            (setMessages ?? (() => {}))((prev: Message[]) =>
                              prev.map((msg) => {
                                if (msg.messageId === message.messageId) {
                                  return { ...msg, suggestions };
                                }
                                return msg;
                              }),
                            );
                          } finally {
                            setSuggestionsLoading(false);
                          }
                        }}
                      >
                        {message.suggestions && message.suggestions.length > 0 ? '重新生成' : '生成建议'}
                      </SuggestionsButton>
                    </div>
                    {message.suggestions && message.suggestions.length > 0 && (
                      <div className="flex flex-col space-y-2">
                        {message.suggestions.map((suggestion, i) => (
                          <div className="flex flex-col space-y-2 text-sm" key={i}>
                            <div className="h-px w-full bg-light-secondary/60 dark:bg-dark-secondary/60" />
                            <div
                              onClick={() => (sendMessage ?? (() => {}))(suggestion)}
                              className="cursor-pointer flex flex-row justify-between font-normal space-x-2 items-center"
                            >
                              <p className="transition duration-200 hover:text-[#24A0ED] text-sm">
                                {suggestion}
                              </p>
                              <Plus size={16} className="text-[#24A0ED] flex-shrink-0" />
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MessageBox;
