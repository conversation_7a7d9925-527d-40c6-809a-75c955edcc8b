import { Check, ClipboardList } from 'lucide-react';
import { Message } from '../../shared/types';
import { useState } from 'react';

const Copy = ({
  message,
  initialMessage,
}: {
  message: Message;
  initialMessage: string;
}) => {
  const [copied, setCopied] = useState(false);

  return (
    <button
      onClick={() => {
        const contentToCopy = `${initialMessage}${message.sources && message.sources.length > 0 && `\n\nCitations:\n${message.sources?.map((source: any, i: any) => `[${i + 1}] ${source.metadata.url}`).join(`\n`)}`}`;
        navigator.clipboard.writeText(contentToCopy);
        setCopied(true);
        setTimeout(() => setCopied(false), 1500);
      }}
      className="p-2 text-black/70 dark:text-white/70 rounded-xl hover:bg-light-secondary dark:hover:bg-dark-secondary transition duration-200 hover:text-black dark:hover:text-white relative group"
    >
      {copied ? (
        <div className="flex items-center">
          <Check size={18} />
          <span className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black/70 dark:bg-white/70 text-white dark:text-black px-2 py-1 rounded text-xs whitespace-nowrap">
            复制至剪贴板
          </span>
        </div>
      ) : (
        <ClipboardList size={18} />
      )}
    </button>
  );
};

export default Copy;
